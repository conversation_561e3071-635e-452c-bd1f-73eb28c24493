{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/convex/_generated/api.js"], "sourcesContent": ["/* eslint-disable */\n/**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */\n\nimport { anyApi } from \"convex/server\";\n\n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */\nexport const api = anyApi;\nexport const internal = anyApi;\n"], "names": [], "mappings": "AAAA,kBAAkB,GAClB;;;;;;;CAOC;;;;AAED;AAAA;;AAUO,MAAM,MAAM,wJAAA,CAAA,SAAM;AAClB,MAAM,WAAW,wJAAA,CAAA,SAAM", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/app/api/test/api-key/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { ConvexHttpClient } from 'convex/browser';\nimport { api } from '../../../../../convex/_generated/api';\n\n// Initialize Convex client for server-side operations\nconst convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);\n\n/**\n * GET /api/test/api-key\n * Test API key creation and validation\n */\nexport async function GET(request: NextRequest) {\n  try {\n    // Create a test API key\n    const testApiKey = await convex.mutation(api.apiKeys.createApiKey, {\n      name: 'Test API Key',\n      permissions: ['products:read', 'products:write'],\n      adminId: 'j970ahapnwvw27zy0rt6vxm5fs7j4ndd' as any, // Use your admin ID\n      environment: 'live' as const,\n    });\n\n    console.log('Created test API key:', testApiKey);\n\n    // Immediately try to validate the created key\n    const validationResult = await convex.query(api.apiKeys.validateApiKey, {\n      key: testApiKey.key\n    });\n\n    console.log('Validation result:', validationResult);\n\n    // Get all API keys to see what's in the database\n    const allApiKeys = await convex.query(api.apiKeys.getApiKeys);\n    \n    return NextResponse.json({\n      success: true,\n      testApiKey: {\n        id: testApiKey.id,\n        keyId: testApiKey.keyId,\n        key: testApiKey.key,\n        environment: testApiKey.environment,\n        name: testApiKey.name,\n        permissions: testApiKey.permissions\n      },\n      validationResult,\n      validationSuccess: validationResult !== null,\n      allApiKeysCount: allApiKeys.length,\n      message: validationResult ? 'API key created and validated successfully!' : 'API key created but validation failed!'\n    });\n\n  } catch (error) {\n    console.error('Test API error:', error);\n    return NextResponse.json(\n      { \n        success: false,\n        error: 'Test failed',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;;;;AAEA,sDAAsD;AACtD,MAAM,SAAS,IAAI,iKAAA,CAAA,mBAAgB;AAM5B,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,wBAAwB;QACxB,MAAM,aAAa,MAAM,OAAO,QAAQ,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,YAAY,EAAE;YACjE,MAAM;YACN,aAAa;gBAAC;gBAAiB;aAAiB;YAChD,SAAS;YACT,aAAa;QACf;QAEA,QAAQ,GAAG,CAAC,yBAAyB;QAErC,8CAA8C;QAC9C,MAAM,mBAAmB,MAAM,OAAO,KAAK,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,cAAc,EAAE;YACtE,KAAK,WAAW,GAAG;QACrB;QAEA,QAAQ,GAAG,CAAC,sBAAsB;QAElC,iDAAiD;QACjD,MAAM,aAAa,MAAM,OAAO,KAAK,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,UAAU;QAE5D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,YAAY;gBACV,IAAI,WAAW,EAAE;gBACjB,OAAO,WAAW,KAAK;gBACvB,KAAK,WAAW,GAAG;gBACnB,aAAa,WAAW,WAAW;gBACnC,MAAM,WAAW,IAAI;gBACrB,aAAa,WAAW,WAAW;YACrC;YACA;YACA,mBAAmB,qBAAqB;YACxC,iBAAiB,WAAW,MAAM;YAClC,SAAS,mBAAmB,gDAAgD;QAC9E;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}