{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_185a879a._.js", "server/edge/chunks/[root-of-the-server]__7adad445._.js", "server/edge/chunks/edge-wrapper_6c593f21.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "93q2+ThywVMICBZGrjqWrlW2FJpdwcjpUekbGqXbIAA=", "__NEXT_PREVIEW_MODE_ID": "96e609d4678f309123ef00282fafae11", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "cfdcc6419a6aefcd0bf9288b55f8e179f6286dd320793443178a66719d4d08e7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d24182a978d2f4dc77fa900a74bafe9d372c6dc53d52151216ba2c7692e47f15"}}}, "sortedMiddleware": ["/"], "functions": {}}