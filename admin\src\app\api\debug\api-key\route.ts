import { NextRequest, NextResponse } from 'next/server';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '../../../../../convex/_generated/api';

// Initialize Convex client for server-side operations
const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

/**
 * GET /api/debug/api-key
 * Debug endpoint to check API key validation
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const testKey = url.searchParams.get('key');
    
    if (!testKey) {
      return NextResponse.json({
        error: 'Please provide a key parameter',
        example: '/api/debug/api-key?key=bzk_live_...'
      });
    }

    // Get all API keys for debugging
    const allApiKeys = await convex.query(api.apiKeys.getApiKeys);
    
    // Try to validate the specific key
    const validationResult = await convex.query(api.apiKeys.validateApiKey, {
      key: testKey
    });

    // Also try to find the key manually without using the index
    const allApiKeysDetailed = await convex.query(api.apiKeys.getApiKeys);
    const manualMatch = allApiKeysDetailed.find(key => key.key === testKey);

    return NextResponse.json({
      testKey,
      testKeyLength: testKey.length,
      validationResult,
      manualMatch: manualMatch ? {
        keyId: manualMatch.keyId,
        name: manualMatch.name,
        isActive: manualMatch.isActive,
        environment: manualMatch.environment,
        permissions: manualMatch.permissions,
        keyMatches: manualMatch.key === testKey,
        actualKey: manualMatch.key,
        keyLength: manualMatch.key ? manualMatch.key.length : 0
      } : null,
      allApiKeysCount: allApiKeysDetailed.length,
      allApiKeys: allApiKeysDetailed.map(key => ({
        keyId: key.keyId,
        name: key.name,
        isActive: key.isActive,
        environment: key.environment,
        permissions: key.permissions,
        // Show first and last 4 characters of the key for debugging
        keyPreview: key.key ? `${key.key.substring(0, 4)}...${key.key.substring(key.key.length - 4)}` : 'No key',
        keyLength: key.key ? key.key.length : 0,
        exactMatch: key.key === testKey,
        actualKey: key.key // Show full key for debugging
      }))
    });

  } catch (error) {
    console.error('Debug API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
