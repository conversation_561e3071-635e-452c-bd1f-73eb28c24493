import { NextRequest, NextResponse } from 'next/server';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '../../../../../convex/_generated/api';

// Initialize Convex client for server-side operations
const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

/**
 * GET /api/test/api-key
 * Test API key creation and validation
 */
export async function GET(request: NextRequest) {
  try {
    // Create a test API key
    const testApiKey = await convex.mutation(api.apiKeys.createApiKey, {
      name: 'Test API Key',
      permissions: ['products:read', 'products:write'],
      adminId: 'j970ahapnwvw27zy0rt6vxm5fs7j4ndd' as any, // Use your admin ID
      environment: 'live' as const,
    });

    console.log('Created test API key:', testApiKey);

    // Immediately try to validate the created key
    const validationResult = await convex.query(api.apiKeys.validateApiKey, {
      key: testApiKey.key
    });

    console.log('Validation result:', validationResult);

    // Get all API keys to see what's in the database
    const allApiKeys = await convex.query(api.apiKeys.getApiKeys);
    
    return NextResponse.json({
      success: true,
      testApiKey: {
        id: testApiKey.id,
        keyId: testApiKey.keyId,
        key: testApiKey.key,
        environment: testApiKey.environment,
        name: testApiKey.name,
        permissions: testApiKey.permissions
      },
      validationResult,
      validationSuccess: validationResult !== null,
      allApiKeysCount: allApiKeys.length,
      message: validationResult ? 'API key created and validated successfully!' : 'API key created but validation failed!'
    });

  } catch (error) {
    console.error('Test API error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
