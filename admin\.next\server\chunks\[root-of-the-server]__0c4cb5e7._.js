module.exports = {

"[project]/.next-internal/server/app/api/v1/collections/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/module [external] (module, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("module", () => require("module"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/convex/_generated/api.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable */ /**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */ __turbopack_context__.s({
    "api": (()=>api),
    "internal": (()=>internal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/server/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/server/api.js [app-route] (ecmascript)");
;
const api = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["anyApi"];
const internal = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["anyApi"];
}}),
"[project]/src/lib/apiKeyAuth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "checkRateLimit": (()=>checkRateLimit),
    "checkSuspiciousActivity": (()=>checkSuspiciousActivity),
    "createApiKeyErrorResponse": (()=>createApiKeyErrorResponse),
    "extractApiKey": (()=>extractApiKey),
    "hasPermission": (()=>hasPermission),
    "logApiKeyUsage": (()=>logApiKeyUsage),
    "logSecurityEvent": (()=>logSecurityEvent),
    "updateRateLimitCounters": (()=>updateRateLimitCounters),
    "validateApiKey": (()=>validateApiKey),
    "withApiKeyAuth": (()=>withApiKeyAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$index$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/index-node.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$http_client$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/http_client.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/convex/_generated/api.js [app-route] (ecmascript)");
;
;
;
// Initialize Convex client for server-side operations
const convex = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$http_client$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ConvexHttpClient"](("TURBOPACK compile-time value", "https://outstanding-quail-54.convex.cloud"));
function extractApiKey(request) {
    // Check Authorization header (Bearer token)
    const authHeader = request.headers.get('authorization');
    if (authHeader?.startsWith('Bearer ')) {
        return authHeader.substring(7);
    }
    // Check X-API-Key header
    const apiKeyHeader = request.headers.get('x-api-key');
    if (apiKeyHeader) {
        return apiKeyHeader;
    }
    // Check query parameter (less secure, but sometimes needed)
    const url = new URL(request.url);
    const apiKeyParam = url.searchParams.get('api_key');
    if (apiKeyParam) {
        return apiKeyParam;
    }
    return null;
}
async function validateApiKey(apiKey) {
    try {
        if (!apiKey) {
            return {
                isValid: false,
                error: 'API key is required',
                statusCode: 401
            };
        }
        // Validate with Convex
        const validationResult = await convex.query(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].apiKeys.validateApiKey, {
            key: apiKey
        });
        if (!validationResult) {
            return {
                isValid: false,
                error: 'Invalid or expired API key',
                statusCode: 401
            };
        }
        return {
            isValid: true,
            apiKey: validationResult
        };
    } catch (error) {
        console.error('API key validation error:', error);
        return {
            isValid: false,
            error: 'Internal server error during API key validation',
            statusCode: 500
        };
    }
}
function hasPermission(apiKey, requiredPermission) {
    if (!apiKey) return false;
    // Check for wildcard permission (admin access)
    if (apiKey.permissions.includes('*')) return true;
    // Check for specific permission (exact match)
    if (apiKey.permissions.includes(requiredPermission)) return true;
    // Handle format differences: convert dots to colons and vice versa
    const normalizedRequired = requiredPermission.replace(/[:.]/g, ':');
    const alternativeRequired = requiredPermission.replace(/[:.]/g, '.');
    // Check for permission with alternative format
    if (apiKey.permissions.includes(alternativeRequired)) return true;
    // Check for permission category wildcards (e.g., 'products:*' allows 'products:read')
    const [category] = normalizedRequired.split(':');
    if (apiKey.permissions.includes(`${category}:*`) || apiKey.permissions.includes(`${category}.*`)) return true;
    return false;
}
async function checkRateLimit(apiKey) {
    if (!apiKey) {
        return {
            allowed: false,
            error: 'No API key provided'
        };
    }
    const now = Date.now();
    const currentMinute = Math.floor(now / 60000) * 60000; // Round to minute
    const currentHour = Math.floor(now / 3600000) * 3600000; // Round to hour
    const currentDay = Math.floor(now / 86400000) * 86400000; // Round to day
    const counts = apiKey.rateLimitCounts || {
        minute: 0,
        hour: 0,
        day: 0,
        burst: 0
    };
    const resets = apiKey.rateLimitResets || {
        minute: currentMinute,
        hour: currentHour,
        day: currentDay
    };
    // Reset counters if time windows have passed
    if (resets.minute < currentMinute) {
        counts.minute = 0;
        counts.burst = 0;
    }
    if (resets.hour < currentHour) {
        counts.hour = 0;
    }
    if (resets.day < currentDay) {
        counts.day = 0;
    }
    // Check limits
    const limits = apiKey.rateLimit;
    // Check daily limit first (most restrictive long-term)
    if (counts.day >= limits.requestsPerDay) {
        const retryAfter = Math.ceil((currentDay + 86400000 - now) / 1000);
        return {
            allowed: false,
            error: 'Daily rate limit exceeded',
            retryAfter
        };
    }
    // Check hourly limit
    if (counts.hour >= limits.requestsPerHour) {
        const retryAfter = Math.ceil((currentHour + 3600000 - now) / 1000);
        return {
            allowed: false,
            error: 'Hourly rate limit exceeded',
            retryAfter
        };
    }
    // Check burst limit (if configured)
    if (limits.burstLimit && counts.burst >= limits.burstLimit) {
        const retryAfter = Math.ceil((currentMinute + 60000 - now) / 1000);
        return {
            allowed: false,
            error: 'Burst rate limit exceeded',
            retryAfter
        };
    }
    // Check per-minute limit
    if (counts.minute >= limits.requestsPerMinute) {
        const retryAfter = Math.ceil((currentMinute + 60000 - now) / 1000);
        return {
            allowed: false,
            error: 'Per-minute rate limit exceeded',
            retryAfter
        };
    }
    return {
        allowed: true
    };
}
async function updateRateLimitCounters(apiKey) {
    try {
        await convex.mutation(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].apiKeys.updateApiKeyUsage, {
            key: apiKey
        });
    } catch (error) {
        console.error('Failed to update rate limit counters:', error);
    // Don't throw error here as it shouldn't block the request
    }
}
function createApiKeyErrorResponse(error, statusCode = 401, retryAfter) {
    const headers = {
        'Content-Type': 'application/json',
        'X-RateLimit-Error': error
    };
    if (retryAfter) {
        headers['Retry-After'] = retryAfter.toString();
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        success: false,
        error: error,
        code: statusCode === 401 ? 'UNAUTHORIZED' : statusCode === 403 ? 'FORBIDDEN' : statusCode === 429 ? 'RATE_LIMITED' : 'ERROR'
    }, {
        status: statusCode,
        headers
    });
}
async function logApiKeyUsage(apiKey, request, success, error) {
    try {
        // Extract relevant request information
        const requestInfo = {
            method: request.method,
            url: request.url,
            userAgent: request.headers.get('user-agent'),
            ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
            timestamp: Date.now(),
            success,
            error,
            keyId: apiKey?.keyId,
            environment: apiKey?.environment
        };
        // Log to console for now (in production, you might want to use a proper logging service)
        console.log('API Key Usage:', JSON.stringify(requestInfo, null, 2));
        // Log security events for failed attempts
        if (!success && error) {
            await logSecurityEvent(request, apiKey, error);
        }
        // Check for suspicious patterns
        await checkSuspiciousActivity(request, apiKey, success);
    } catch (logError) {
        console.error('Failed to log API key usage:', logError);
    // Don't throw error here as it shouldn't block the request
    }
}
async function logSecurityEvent(request, apiKey, error) {
    try {
        let eventType;
        let severity;
        let description;
        // Determine event type and severity based on error
        if (error.includes('API key is required') || error.includes('Invalid API key')) {
            eventType = 'invalid_api_key';
            severity = 'medium';
            description = 'Invalid or missing API key attempt';
        } else if (error.includes('Rate limit exceeded')) {
            eventType = 'rate_limit_exceeded';
            severity = 'low';
            description = 'API rate limit exceeded';
        } else if (error.includes('Insufficient permissions')) {
            eventType = 'permission_violation';
            severity = 'medium';
            description = 'API key attempted to access unauthorized resource';
        } else {
            eventType = 'suspicious_usage_pattern';
            severity = 'low';
            description = `API request failed: ${error}`;
        }
        const details = {
            error,
            timestamp: Date.now(),
            requestPath: new URL(request.url).pathname,
            hasApiKey: !!apiKey
        };
        await convex.mutation(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].security.logSecurityEvent, {
            eventType: eventType,
            severity: severity,
            description,
            details,
            ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
            userAgent: request.headers.get('user-agent') || undefined,
            requestUrl: request.url,
            requestMethod: request.method,
            apiKeyId: apiKey?.keyId,
            apiKeyEnvironment: apiKey?.environment
        });
    } catch (error) {
        console.error('Failed to log security event:', error);
    }
}
async function checkSuspiciousActivity(request, apiKey, success) {
    try {
        const ip = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
        // Check for multiple failed attempts from same IP
        if (!success) {
            // In a real implementation, you would track failed attempts in a cache or database
            // For now, we'll just log a potential security event
            await convex.mutation(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].security.logSecurityEvent, {
                eventType: 'multiple_failed_attempts',
                severity: 'medium',
                description: 'Failed API request detected',
                details: {
                    ip,
                    userAgent: request.headers.get('user-agent'),
                    requestPath: new URL(request.url).pathname,
                    timestamp: Date.now()
                },
                ipAddress: ip,
                userAgent: request.headers.get('user-agent') || undefined,
                requestUrl: request.url,
                requestMethod: request.method,
                apiKeyId: apiKey?.keyId,
                apiKeyEnvironment: apiKey?.environment
            });
        }
        // Check for unusual usage patterns (simplified example)
        if (apiKey && success) {
            const hour = new Date().getHours();
            // Flag requests outside normal business hours as potentially suspicious
            if (hour < 6 || hour > 22) {
                await convex.mutation(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].security.logSecurityEvent, {
                    eventType: 'unusual_ip_activity',
                    severity: 'low',
                    description: 'API request outside normal business hours',
                    details: {
                        hour,
                        ip,
                        keyId: apiKey.keyId,
                        timestamp: Date.now()
                    },
                    ipAddress: ip,
                    userAgent: request.headers.get('user-agent') || undefined,
                    requestUrl: request.url,
                    requestMethod: request.method,
                    apiKeyId: apiKey.keyId,
                    apiKeyEnvironment: apiKey.environment
                });
            }
        }
    } catch (error) {
        console.error('Failed to check suspicious activity:', error);
    }
}
function withApiKeyAuth(handler, options = {}) {
    return async (request)=>{
        try {
            // Extract API key from request
            const apiKeyString = extractApiKey(request);
            if (!apiKeyString) {
                await logApiKeyUsage(undefined, request, false, 'No API key provided');
                return createApiKeyErrorResponse('API key is required');
            }
            // Validate API key
            const validation = await validateApiKey(apiKeyString);
            if (!validation.isValid) {
                await logApiKeyUsage(undefined, request, false, validation.error);
                return createApiKeyErrorResponse(validation.error || 'Invalid API key', validation.statusCode || 401);
            }
            // Check permissions if required
            if (options.requiredPermission && !hasPermission(validation.apiKey, options.requiredPermission)) {
                await logApiKeyUsage(validation.apiKey, request, false, 'Insufficient permissions');
                return createApiKeyErrorResponse('Insufficient permissions', 403);
            }
            // Check rate limits (unless skipped)
            if (!options.skipRateLimit) {
                const rateLimitCheck = await checkRateLimit(validation.apiKey);
                if (!rateLimitCheck.allowed) {
                    await logApiKeyUsage(validation.apiKey, request, false, rateLimitCheck.error);
                    return createApiKeyErrorResponse(rateLimitCheck.error || 'Rate limit exceeded', 429, rateLimitCheck.retryAfter);
                }
                // Update rate limit counters
                await updateRateLimitCounters(apiKeyString);
            }
            // Log successful authentication
            await logApiKeyUsage(validation.apiKey, request, true);
            // Call the actual handler with validated API key
            return await handler(request, validation.apiKey);
        } catch (error) {
            console.error('API key middleware error:', error);
            await logApiKeyUsage(undefined, request, false, 'Internal server error');
            return createApiKeyErrorResponse('Internal server error', 500);
        }
    };
}
}}),
"[project]/src/app/api/v1/collections/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "OPTIONS": (()=>OPTIONS),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$index$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/index-node.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$http_client$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/http_client.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/convex/_generated/api.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiKeyAuth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiKeyAuth.ts [app-route] (ecmascript)");
;
;
;
;
// Initialize Convex client for server-side operations
const convex = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$http_client$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ConvexHttpClient"](("TURBOPACK compile-time value", "https://outstanding-quail-54.convex.cloud"));
const GET = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiKeyAuth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withApiKeyAuth"])(async (request, apiKey)=>{
    try {
        const url = new URL(request.url);
        const limit = parseInt(url.searchParams.get('limit') || '50');
        const offset = parseInt(url.searchParams.get('offset') || '0');
        const search = url.searchParams.get('search') || undefined;
        const status = url.searchParams.get('status') || undefined;
        const isVisible = url.searchParams.get('visible') === 'true' ? true : url.searchParams.get('visible') === 'false' ? false : undefined;
        // Validate limit
        if (limit > 100) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Limit cannot exceed 100 items per request',
                code: 'INVALID_LIMIT'
            }, {
                status: 400
            });
        }
        // Get collections from Convex
        const collections = await convex.query(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].collections.getCollections, {
            limit,
            offset,
            search,
            status: status,
            isVisible
        });
        // Transform collections for API response
        const apiCollections = collections.map((collection)=>({
                id: collection.collectionId,
                title: collection.title,
                description: collection.description,
                handle: collection.handle,
                image: collection.image,
                seoTitle: collection.seoTitle,
                seoDescription: collection.seoDescription,
                status: collection.status,
                sortOrder: collection.sortOrder,
                isVisible: collection.isVisible,
                productCount: collection.productCount,
                createdAt: collection.createdAt,
                updatedAt: collection.updatedAt
            }));
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: apiCollections,
            pagination: {
                limit,
                offset,
                total: apiCollections.length,
                hasMore: apiCollections.length === limit
            },
            meta: {
                apiKeyId: apiKey.keyId,
                environment: apiKey.environment,
                timestamp: Date.now()
            }
        });
    } catch (error) {
        console.error('Collections API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Internal server error',
            code: 'INTERNAL_ERROR'
        }, {
            status: 500
        });
    }
}, {
    requiredPermission: 'collections:read'
});
const POST = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiKeyAuth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withApiKeyAuth"])(async (request, apiKey)=>{
    try {
        const body = await request.json();
        // Validate required fields
        const requiredFields = [
            'title',
            'handle'
        ];
        for (const field of requiredFields){
            if (!body[field]) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    error: `Missing required field: ${field}`,
                    code: 'MISSING_FIELD'
                }, {
                    status: 400
                });
            }
        }
        // Create collection via Convex
        const collectionId = await convex.mutation(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].collections.createCollection, {
            ...body,
            createdBy: apiKey.id
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                id: collectionId,
                message: 'Collection created successfully'
            },
            meta: {
                apiKeyId: apiKey.keyId,
                environment: apiKey.environment,
                timestamp: Date.now()
            }
        }, {
            status: 201
        });
    } catch (error) {
        console.error('Create collection API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to create collection',
            code: 'CREATE_FAILED'
        }, {
            status: 500
        });
    }
}, {
    requiredPermission: 'collections:write'
});
async function OPTIONS(request) {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
            'Access-Control-Max-Age': '86400'
        }
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__0c4cb5e7._.js.map