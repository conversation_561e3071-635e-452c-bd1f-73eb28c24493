module.exports = {

"[project]/.next-internal/server/app/api/v1/status/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/v1/status/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "OPTIONS": (()=>OPTIONS)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
async function GET(request) {
    try {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                status: 'operational',
                version: '1.0.0',
                environment: ("TURBOPACK compile-time value", "development") || 'development',
                timestamp: Date.now(),
                uptime: process.uptime()
            },
            api: {
                name: 'Benzochem Industries API',
                description: 'Professional API for chemical products and collections management',
                documentation: '/api/v1/docs',
                authentication: {
                    type: 'API Key',
                    methods: [
                        'Authorization: Bearer <api_key>',
                        'X-API-Key: <api_key>',
                        'Query parameter: ?api_key=<api_key> (not recommended for production)'
                    ]
                },
                endpoints: {
                    products: {
                        list: 'GET /api/v1/products',
                        create: 'POST /api/v1/products',
                        get: 'GET /api/v1/products/{id}',
                        update: 'PUT /api/v1/products/{id}',
                        delete: 'DELETE /api/v1/products/{id}'
                    },
                    collections: {
                        list: 'GET /api/v1/collections',
                        create: 'POST /api/v1/collections',
                        get: 'GET /api/v1/collections/{id}',
                        update: 'PUT /api/v1/collections/{id}',
                        delete: 'DELETE /api/v1/collections/{id}'
                    },
                    analytics: {
                        overview: 'GET /api/v1/analytics/overview',
                        products: 'GET /api/v1/analytics/products',
                        usage: 'GET /api/v1/analytics/usage'
                    }
                },
                permissions: {
                    'products:read': 'Read product information',
                    'products:write': 'Create and update products',
                    'products:delete': 'Delete products',
                    'collections:read': 'Read collection information',
                    'collections:write': 'Create and update collections',
                    'collections:delete': 'Delete collections',
                    'analytics:read': 'Read analytics data',
                    'webhooks:read': 'Read webhook configurations',
                    'webhooks:write': 'Create and update webhooks'
                },
                rateLimits: {
                    standard: {
                        requestsPerMinute: 100,
                        requestsPerHour: 5000,
                        requestsPerDay: 50000,
                        burstLimit: 150
                    },
                    premium: {
                        requestsPerMinute: 500,
                        requestsPerHour: 25000,
                        requestsPerDay: 250000,
                        burstLimit: 750
                    },
                    enterprise: {
                        requestsPerMinute: 2000,
                        requestsPerHour: 100000,
                        requestsPerDay: 1000000,
                        burstLimit: 3000
                    }
                },
                errorCodes: {
                    'UNAUTHORIZED': 'API key is missing or invalid',
                    'FORBIDDEN': 'API key lacks required permissions',
                    'RATE_LIMITED': 'Rate limit exceeded',
                    'INVALID_LIMIT': 'Request limit parameter is invalid',
                    'MISSING_FIELD': 'Required field is missing from request',
                    'CREATE_FAILED': 'Failed to create resource',
                    'UPDATE_FAILED': 'Failed to update resource',
                    'DELETE_FAILED': 'Failed to delete resource',
                    'NOT_FOUND': 'Resource not found',
                    'INTERNAL_ERROR': 'Internal server error'
                }
            },
            support: {
                documentation: 'https://docs.benzochem.com/api',
                contact: '<EMAIL>',
                status: 'https://status.benzochem.com'
            }
        });
    } catch (error) {
        console.error('Status API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to get API status',
            code: 'INTERNAL_ERROR'
        }, {
            status: 500
        });
    }
}
async function OPTIONS(request) {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
            'Access-Control-Max-Age': '86400'
        }
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__22a07348._.js.map