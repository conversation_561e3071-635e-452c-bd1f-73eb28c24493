{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/convex/_generated/api.js"], "sourcesContent": ["/* eslint-disable */\n/**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */\n\nimport { anyApi } from \"convex/server\";\n\n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */\nexport const api = anyApi;\nexport const internal = anyApi;\n"], "names": [], "mappings": "AAAA,kBAAkB,GAClB;;;;;;;CAOC;;;;AAED;AAAA;;AAUO,MAAM,MAAM,wJAAA,CAAA,SAAM;AAClB,MAAM,WAAW,wJAAA,CAAA,SAAM", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/app/api/debug/api-key/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { ConvexHttpClient } from 'convex/browser';\nimport { api } from '../../../../../convex/_generated/api';\n\n// Initialize Convex client for server-side operations\nconst convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);\n\n/**\n * GET /api/debug/api-key\n * Debug endpoint to check API key validation\n */\nexport async function GET(request: NextRequest) {\n  try {\n    const url = new URL(request.url);\n    const testKey = url.searchParams.get('key');\n    \n    if (!testKey) {\n      return NextResponse.json({\n        error: 'Please provide a key parameter',\n        example: '/api/debug/api-key?key=bzk_live_...'\n      });\n    }\n\n    // Get all API keys for debugging (this will show masked keys)\n    const allApiKeys = await convex.query(api.apiKeys.getApiKeys);\n\n    // Try to validate the specific key\n    const validationResult = await convex.query(api.apiKeys.validateApiKey, {\n      key: testKey\n    });\n\n    // Get full API keys for debugging (this should show actual keys)\n    const allApiKeysDetailed = await convex.query(api.apiKeys.getApiKeys);\n\n    // If we have API keys, try to get the full version of the first one for debugging\n    let fullApiKey = null;\n    if (allApiKeysDetailed.length > 0) {\n      try {\n        fullApiKey = await convex.query(api.apiKeys.getFullApiKeyById, {\n          id: allApiKeysDetailed[0]._id\n        });\n      } catch (error) {\n        console.error('Error getting full API key:', error);\n      }\n    }\n    const manualMatch = allApiKeysDetailed.find(key => key.key === testKey);\n\n    return NextResponse.json({\n      testKey,\n      testKeyLength: testKey.length,\n      validationResult,\n      manualMatch: manualMatch ? {\n        keyId: manualMatch.keyId,\n        name: manualMatch.name,\n        isActive: manualMatch.isActive,\n        environment: manualMatch.environment,\n        permissions: manualMatch.permissions,\n        keyMatches: manualMatch.key === testKey,\n        actualKey: manualMatch.key, // Show full key for debugging\n        keyLength: manualMatch.key ? manualMatch.key.length : 0\n      } : null,\n      fullApiKey: fullApiKey ? {\n        keyId: fullApiKey.keyId,\n        name: fullApiKey.name,\n        isActive: fullApiKey.isActive,\n        environment: fullApiKey.environment,\n        permissions: fullApiKey.permissions,\n        actualKey: fullApiKey.key, // This should show the full key\n        keyLength: fullApiKey.key ? fullApiKey.key.length : 0\n      } : null,\n      allApiKeysCount: allApiKeysDetailed.length,\n      allApiKeys: allApiKeysDetailed.map(key => ({\n        keyId: key.keyId,\n        name: key.name,\n        isActive: key.isActive,\n        environment: key.environment,\n        permissions: key.permissions,\n        // Show first and last 4 characters of the key for debugging\n        keyPreview: key.key ? `${key.key.substring(0, 4)}...${key.key.substring(key.key.length - 4)}` : 'No key',\n        keyLength: key.key ? key.key.length : 0,\n        exactMatch: key.key === testKey,\n        actualKey: key.key // Show full key for debugging - REMOVE IN PRODUCTION!\n      }))\n    });\n\n  } catch (error) {\n    console.error('Debug API error:', error);\n    return NextResponse.json(\n      { \n        error: 'Internal server error',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;;;;AAEA,sDAAsD;AACtD,MAAM,SAAS,IAAI,iKAAA,CAAA,mBAAgB;AAM5B,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;QAC/B,MAAM,UAAU,IAAI,YAAY,CAAC,GAAG,CAAC;QAErC,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;gBACP,SAAS;YACX;QACF;QAEA,8DAA8D;QAC9D,MAAM,aAAa,MAAM,OAAO,KAAK,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,UAAU;QAE5D,mCAAmC;QACnC,MAAM,mBAAmB,MAAM,OAAO,KAAK,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,cAAc,EAAE;YACtE,KAAK;QACP;QAEA,iEAAiE;QACjE,MAAM,qBAAqB,MAAM,OAAO,KAAK,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,UAAU;QAEpE,kFAAkF;QAClF,IAAI,aAAa;QACjB,IAAI,mBAAmB,MAAM,GAAG,GAAG;YACjC,IAAI;gBACF,aAAa,MAAM,OAAO,KAAK,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,iBAAiB,EAAE;oBAC7D,IAAI,kBAAkB,CAAC,EAAE,CAAC,GAAG;gBAC/B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;YAC/C;QACF;QACA,MAAM,cAAc,mBAAmB,IAAI,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK;QAE/D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,eAAe,QAAQ,MAAM;YAC7B;YACA,aAAa,cAAc;gBACzB,OAAO,YAAY,KAAK;gBACxB,MAAM,YAAY,IAAI;gBACtB,UAAU,YAAY,QAAQ;gBAC9B,aAAa,YAAY,WAAW;gBACpC,aAAa,YAAY,WAAW;gBACpC,YAAY,YAAY,GAAG,KAAK;gBAChC,WAAW,YAAY,GAAG;gBAC1B,WAAW,YAAY,GAAG,GAAG,YAAY,GAAG,CAAC,MAAM,GAAG;YACxD,IAAI;YACJ,YAAY,aAAa;gBACvB,OAAO,WAAW,KAAK;gBACvB,MAAM,WAAW,IAAI;gBACrB,UAAU,WAAW,QAAQ;gBAC7B,aAAa,WAAW,WAAW;gBACnC,aAAa,WAAW,WAAW;gBACnC,WAAW,WAAW,GAAG;gBACzB,WAAW,WAAW,GAAG,GAAG,WAAW,GAAG,CAAC,MAAM,GAAG;YACtD,IAAI;YACJ,iBAAiB,mBAAmB,MAAM;YAC1C,YAAY,mBAAmB,GAAG,CAAC,CAAA,MAAO,CAAC;oBACzC,OAAO,IAAI,KAAK;oBAChB,MAAM,IAAI,IAAI;oBACd,UAAU,IAAI,QAAQ;oBACtB,aAAa,IAAI,WAAW;oBAC5B,aAAa,IAAI,WAAW;oBAC5B,4DAA4D;oBAC5D,YAAY,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,EAAE,IAAI,GAAG,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,GAAG;oBAChG,WAAW,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG;oBACtC,YAAY,IAAI,GAAG,KAAK;oBACxB,WAAW,IAAI,GAAG,CAAC,sDAAsD;gBAC3E,CAAC;QACH;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}