{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/app/api/v1/status/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\n/**\n * GET /api/v1/status\n * Public endpoint to check API status and get information\n */\nexport async function GET(request: NextRequest) {\n  try {\n    return NextResponse.json({\n      success: true,\n      data: {\n        status: 'operational',\n        version: '1.0.0',\n        environment: process.env.NODE_ENV || 'development',\n        timestamp: Date.now(),\n        uptime: process.uptime(),\n      },\n      api: {\n        name: 'Benzochem Industries API',\n        description: 'Professional API for chemical products and collections management',\n        documentation: '/api/v1/docs',\n        authentication: {\n          type: 'API Key',\n          methods: [\n            'Authorization: Bearer <api_key>',\n            'X-API-Key: <api_key>',\n            'Query parameter: ?api_key=<api_key> (not recommended for production)'\n          ]\n        },\n        endpoints: {\n          products: {\n            list: 'GET /api/v1/products',\n            create: 'POST /api/v1/products',\n            get: 'GET /api/v1/products/{id}',\n            update: 'PUT /api/v1/products/{id}',\n            delete: 'DELETE /api/v1/products/{id}'\n          },\n          collections: {\n            list: 'GET /api/v1/collections',\n            create: 'POST /api/v1/collections',\n            get: 'GET /api/v1/collections/{id}',\n            update: 'PUT /api/v1/collections/{id}',\n            delete: 'DELETE /api/v1/collections/{id}'\n          },\n          analytics: {\n            overview: 'GET /api/v1/analytics/overview',\n            products: 'GET /api/v1/analytics/products',\n            usage: 'GET /api/v1/analytics/usage'\n          }\n        },\n        permissions: {\n          'products:read': 'Read product information',\n          'products:write': 'Create and update products',\n          'products:delete': 'Delete products',\n          'collections:read': 'Read collection information',\n          'collections:write': 'Create and update collections',\n          'collections:delete': 'Delete collections',\n          'analytics:read': 'Read analytics data',\n          'webhooks:read': 'Read webhook configurations',\n          'webhooks:write': 'Create and update webhooks'\n        },\n        rateLimits: {\n          standard: {\n            requestsPerMinute: 100,\n            requestsPerHour: 5000,\n            requestsPerDay: 50000,\n            burstLimit: 150\n          },\n          premium: {\n            requestsPerMinute: 500,\n            requestsPerHour: 25000,\n            requestsPerDay: 250000,\n            burstLimit: 750\n          },\n          enterprise: {\n            requestsPerMinute: 2000,\n            requestsPerHour: 100000,\n            requestsPerDay: 1000000,\n            burstLimit: 3000\n          }\n        },\n        errorCodes: {\n          'UNAUTHORIZED': 'API key is missing or invalid',\n          'FORBIDDEN': 'API key lacks required permissions',\n          'RATE_LIMITED': 'Rate limit exceeded',\n          'INVALID_LIMIT': 'Request limit parameter is invalid',\n          'MISSING_FIELD': 'Required field is missing from request',\n          'CREATE_FAILED': 'Failed to create resource',\n          'UPDATE_FAILED': 'Failed to update resource',\n          'DELETE_FAILED': 'Failed to delete resource',\n          'NOT_FOUND': 'Resource not found',\n          'INTERNAL_ERROR': 'Internal server error'\n        }\n      },\n      support: {\n        documentation: 'https://docs.benzochem.com/api',\n        contact: '<EMAIL>',\n        status: 'https://status.benzochem.com'\n      }\n    });\n\n  } catch (error) {\n    console.error('Status API error:', error);\n    return NextResponse.json(\n      { \n        success: false, \n        error: 'Failed to get API status',\n        code: 'INTERNAL_ERROR'\n      },\n      { status: 500 }\n    );\n  }\n}\n\n/**\n * OPTIONS handler for CORS\n */\nexport async function OPTIONS(request: NextRequest) {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type',\n      'Access-Control-Max-Age': '86400',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAMO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,QAAQ;gBACR,SAAS;gBACT,aAAa,mDAAwB;gBACrC,WAAW,KAAK,GAAG;gBACnB,QAAQ,QAAQ,MAAM;YACxB;YACA,KAAK;gBACH,MAAM;gBACN,aAAa;gBACb,eAAe;gBACf,gBAAgB;oBACd,MAAM;oBACN,SAAS;wBACP;wBACA;wBACA;qBACD;gBACH;gBACA,WAAW;oBACT,UAAU;wBACR,MAAM;wBACN,QAAQ;wBACR,KAAK;wBACL,QAAQ;wBACR,QAAQ;oBACV;oBACA,aAAa;wBACX,MAAM;wBACN,QAAQ;wBACR,KAAK;wBACL,QAAQ;wBACR,QAAQ;oBACV;oBACA,WAAW;wBACT,UAAU;wBACV,UAAU;wBACV,OAAO;oBACT;gBACF;gBACA,aAAa;oBACX,iBAAiB;oBACjB,kBAAkB;oBAClB,mBAAmB;oBACnB,oBAAoB;oBACpB,qBAAqB;oBACrB,sBAAsB;oBACtB,kBAAkB;oBAClB,iBAAiB;oBACjB,kBAAkB;gBACpB;gBACA,YAAY;oBACV,UAAU;wBACR,mBAAmB;wBACnB,iBAAiB;wBACjB,gBAAgB;wBAChB,YAAY;oBACd;oBACA,SAAS;wBACP,mBAAmB;wBACnB,iBAAiB;wBACjB,gBAAgB;wBAChB,YAAY;oBACd;oBACA,YAAY;wBACV,mBAAmB;wBACnB,iBAAiB;wBACjB,gBAAgB;wBAChB,YAAY;oBACd;gBACF;gBACA,YAAY;oBACV,gBAAgB;oBAChB,aAAa;oBACb,gBAAgB;oBAChB,iBAAiB;oBACjB,iBAAiB;oBACjB,iBAAiB;oBACjB,iBAAiB;oBACjB,iBAAiB;oBACjB,aAAa;oBACb,kBAAkB;gBACpB;YACF;YACA,SAAS;gBACP,eAAe;gBACf,SAAS;gBACT,QAAQ;YACV;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,MAAM;QACR,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAKO,eAAe,QAAQ,OAAoB;IAChD,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;YAChC,0BAA0B;QAC5B;IACF;AACF", "debugId": null}}]}